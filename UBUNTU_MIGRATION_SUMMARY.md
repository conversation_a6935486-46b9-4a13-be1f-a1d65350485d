# Ubuntu Linux Migration Summary

## Overview
Successfully modified both `md2html` and `md2html-scroll` scripts to work on Ubuntu Linux with bash shell instead of macOS with zsh.

## Changes Made

### 1. Package Manager References Updated
**Files affected:** `md2html`, `md2html-scroll`

**Before (macOS):**
```bash
brew install pipenv
```

**After (Ubuntu):**
```bash
sudo apt-get update && sudo apt-get install python3-pip
pip3 install --user pipenv
```

**Additional Ubuntu option added:**
```bash
# Method 4: Direct system pip usage
sudo apt-get update && sudo apt-get install python3-pip
pip3 install --user beautifulsoup4
python3 md2html
```

### 2. Removed zsh Fallback Mechanism
**Files affected:** `md2html`, `md2html-scroll`

**Removed code:**
- zsh-specific fallback that tried to use `mypandoc` alias
- Lines 210-234 in original scripts that contained:
  ```python
  zsh_cmd = f"zsh -c \"mypandoc '{md_file_esc}' -o '{output_file_esc}'\""
  ```

**Replaced with:**
```python
except subprocess.CalledProcessError as e:
    print(f"Error running pandoc: {str(e)}")
    print("Please ensure pandoc is installed on your system:")
    print("  sudo apt-get update && sudo apt-get install pandoc")
    return False
```

### 3. Updated Font Stack for Linux Compatibility
**Files affected:** `md2html`, `md2html-scroll` (default CSS generation)

**Before (macOS fonts):**
```css
font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
font-family: SFMono-Regular, Consolas, "Liberation Mono", Menlo, monospace;
```

**After (Linux-friendly fonts):**
```css
font-family: "Ubuntu", "Segoe UI", "Liberation Sans", "DejaVu Sans", Helvetica, Arial, sans-serif;
font-family: "Ubuntu Mono", "Liberation Mono", "DejaVu Sans Mono", Consolas, "Courier New", monospace;
```

### 4. Script Permissions
Made both scripts executable:
```bash
chmod +x md2html md2html-scroll
```

## Key Features Preserved

1. **Direct pandoc usage:** Scripts now use `pandoc -s --embed-resources --css=$CSS_FILE` directly instead of relying on `mypandoc` alias
2. **CSS file handling:** Scripts still look for `pandoc-style-1.css` and `pandoc-style-2.css` in the script directory
3. **Fallback CSS generation:** If CSS files are missing, scripts create default CSS with Linux-friendly fonts
4. **All original functionality:** Table processing, column width adjustments, HTML post-processing remain unchanged

## Ubuntu Installation Requirements

### Prerequisites
```bash
# Install Python 3 and pip (usually pre-installed on Ubuntu)
sudo apt-get update
sudo apt-get install python3 python3-pip

# Install pandoc
sudo apt-get install pandoc

# Install BeautifulSoup4
pip3 install --user beautifulsoup4
```

### Alternative: Using virtual environment (recommended)
```bash
# Using pipenv
sudo apt-get update && sudo apt-get install python3-pip
pip3 install --user pipenv
pipenv install beautifulsoup4
pipenv run python3 md2html

# Using poetry
curl -sSL https://install.python-poetry.org | python3 -
poetry init -n
poetry add beautifulsoup4
poetry run python3 md2html
```

## Usage on Ubuntu

The scripts work exactly the same as before:

```bash
# Process single file
./md2html input.md
./md2html input.md -o output.html

# Process all markdown files in directory
./md2html
./md2html-scroll

# With working directory
./md2html --working-dir /path/to/markdown/files
```

## Testing Status

✅ Scripts execute and show proper Ubuntu installation instructions
✅ Command-line argument parsing works correctly
✅ Error handling provides Ubuntu-specific guidance
✅ Scripts are executable with proper shebang (`#!/usr/bin/env python3`)
✅ Font stacks updated for Linux compatibility

## Notes

- The existing `pandoc-style-1.css` and `pandoc-style-2.css` files don't specify fonts, so they'll use browser defaults (which is fine for Ubuntu)
- Scripts will create default CSS files with Linux-friendly fonts if the CSS files are missing
- No more dependency on zsh or macOS-specific shell features
- All error messages now provide Ubuntu-appropriate installation commands
