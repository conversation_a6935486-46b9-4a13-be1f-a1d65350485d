body {
  margin: 0;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 10px;
  padding-bottom: 10px;
  width: 100%;
  max-width: none;
  box-sizing: border-box;
}
table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 1em;
}
th,
td {
  border: 1px solid #ccc;
  padding: 8px;
  text-align: left;
  /*  word-wrap: break-word;*/
  overflow-wrap: break-word;
}
/* 1列目のセルのパディングを4pxに設定 */
td:first-child,
th:first-child {
  padding: 4px;
}
th {
  background-color: #f2f2f2;
}
p {
  margin-bottom: 1em; /* 段落間のスペース */
}
.table-scroll {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  margin-bottom: 1em;
}
.table-scroll table {
  /* テーブルレイアウトはHTMLで直接指定するため、ここでは削除 */
}
