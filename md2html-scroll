#!/usr/bin/env python3

import os
import subprocess
import argparse

try:
    from bs4 import BeautifulSoup
except ImportError:
    print("BeautifulSoup4ライブラリがインストールされていません。")
    print("このスクリプトは仮想環境内で実行することを推奨します。")
    print("以下のいずれかの方法でBeautifulSoup4をインストールしてください：")
    print("\n方法1: pipenvを使用する場合:")
    print("  sudo apt-get update && sudo apt-get install python3-pip")
    print("  pip3 install --user pipenv")
    print("  pipenv install beautifulsoup4")
    print("  pipenv run python md2html-scroll")
    print("\n方法2: poetryを使用する場合:")
    print("  curl -sSL https://install.python-poetry.org | python3 -")
    print("  poetry init -n")
    print("  poetry add beautifulsoup4")
    print("  poetry run python md2html-scroll")
    print("\n方法3: uvを使用する場合:")
    print("  pip3 install --user uv")
    print("  uv venv")
    print("  source .venv/bin/activate")
    print("  uv pip install beautifulsoup4")
    print("  python md2html-scroll")
    print("\n方法4: システムのpipを直接使用する場合:")
    print("  sudo apt-get update && sudo apt-get install python3-pip")
    print("  pip3 install --user beautifulsoup4")
    print("  python3 md2html-scroll")
    exit(1)

def process_html(html_file):
    """Process the HTML file to modify table and colgroup tags."""
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            soup = BeautifulSoup(f.read(), 'html.parser')

        # Check if colgroup exists
        colgroups = soup.find_all('colgroup')
        if not colgroups:
            print(f"Error: {html_file} - Markdownファイルは表形式ではありません。")
            return False

        # 各テーブルにスタイルを追加し、div.table-scrollで囲む
        tables = soup.find_all('table')
        for table in tables:
            # テーブルのスタイルを設定
            table['style'] = "min-width: 800px; width: 100%;"

            # テーブルをdiv.table-scrollで囲む
            table_scroll_div = soup.new_tag('div')
            table_scroll_div['class'] = 'table-scroll'
            table.wrap(table_scroll_div)

        for colgroup in colgroups:
            cols = colgroup.find_all('col')
            num_cols = len(cols)

            if num_cols == 5:
                widths = ["5%", "5%", "25%", "25%", "40%"]
                for i, col in enumerate(cols):
                    col['style'] = f"width: {widths[i]}"
            elif num_cols == 4:
                widths = ["4%", "22%", "24%", "50%"]
                for i, col in enumerate(cols):
                    col['style'] = f"width: {widths[i]}"

        # Save the modified HTML
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(str(soup))

        return True

    except Exception as e:
        print(f"Error processing {html_file}: {str(e)}")
        return False

def convert_md_to_html(md_file, output_file=None):
    """Convert Markdown file to HTML using pandoc."""
    if not md_file.lower().endswith('.md'):
        print(f"Error: {md_file} is not a Markdown file.")
        return False

    if not os.path.exists(md_file):
        print(f"Error: {md_file} does not exist.")
        return False

    # ファイル名の処理を改善
    base_name = os.path.basename(md_file)
    name_without_ext = os.path.splitext(base_name)[0]

    if output_file is None:
        # 出力ファイルは入力ファイルと同じディレクトリに作成
        output_dir = os.path.dirname(md_file)
        output_file = os.path.join(output_dir, f"{name_without_ext}.html")

    try:
        # Run mypandoc to convert Markdown to HTML
        print(f"Converting {md_file} to {output_file}...")
        # mypandocはエイリアスなので、直接pandocコマンドを使用する
        # ファイル名に特殊文字が含まれている場合の処理を改善
        md_file_escaped = md_file.replace("'", "'\''")
        output_file_escaped = output_file.replace("'", "'\''")

        # CSSファイルのパスを取得
        try:
            # スクリプトと同じディレクトリにあるpandoc-style-2.cssファイルを使用
            script_dir = os.path.dirname(os.path.abspath(__file__))
            css_file = os.path.join(script_dir, "pandoc-style-2.css")

            if not os.path.exists(css_file):
                # スクリプトディレクトリにCSSファイルがない場合は、作成する
                print(f"Warning: CSS file {css_file} not found. Creating a default CSS file.")
                default_css = """body {
    font-family: "Ubuntu", "Segoe UI", "Liberation Sans", "DejaVu Sans", Helvetica, Arial, sans-serif;
    line-height: 1.6;
    color: #24292e;
    max-width: 900px;
    margin: 0 auto;
    padding: 2em 1em;
}

.table-scroll {
    overflow-x: auto;
    margin-bottom: 1em;
}

table {
    border-collapse: collapse;
    margin-bottom: 1em;
}

table, th, td {
    border: 1px solid #dfe2e5;
}

th, td {
    padding: 8px 12px;
}

th {
    background-color: #f6f8fa;
}

code {
    font-family: "Ubuntu Mono", "Liberation Mono", "DejaVu Sans Mono", Consolas, "Courier New", monospace;
    background-color: rgba(27, 31, 35, 0.05);
    padding: 0.2em 0.4em;
    border-radius: 3px;
}

pre {
    background-color: #f6f8fa;
    border-radius: 3px;
    padding: 16px;
    overflow: auto;
}

pre code {
    background-color: transparent;
    padding: 0;
}

blockquote {
    margin: 0;
    padding: 0 1em;
    color: #6a737d;
    border-left: 0.25em solid #dfe2e5;
}

h1, h2, h3, h4, h5, h6 {
    margin-top: 24px;
    margin-bottom: 16px;
    font-weight: 600;
    line-height: 1.25;
}

h1 {
    padding-bottom: 0.3em;
    font-size: 2em;
    border-bottom: 1px solid #eaecef;
}

h2 {
    padding-bottom: 0.3em;
    font-size: 1.5em;
    border-bottom: 1px solid #eaecef;
}

a {
    color: #0366d6;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

img {
    max-width: 100%;
}
"""
                with open(css_file, "w", encoding="utf-8") as f:
                    f.write(default_css)
                print(f"Created default CSS file at {css_file}")

            cmd = f"pandoc -s --embed-resources --css='{css_file}' '{md_file_escaped}' -o '{output_file_escaped}'"
        except Exception as e:
            print(f"Warning: Error getting CSS file path: {str(e)}. Running pandoc without CSS.")
            cmd = f"pandoc -s --embed-resources '{md_file_escaped}' -o '{output_file_escaped}'"
        subprocess.run(cmd, shell=True, check=True)

        # Process the generated HTML
        success = process_html(output_file)
        if success:
            print(f"Successfully processed {md_file} to {output_file}")

        return success

    except subprocess.CalledProcessError as e:
        print(f"Error running pandoc: {str(e)}")
        print("Please ensure pandoc is installed on your system:")
        print("  sudo apt-get update && sudo apt-get install pandoc")
        return False
    except Exception as e:
        print(f"Error: {str(e)}")
        return False

def process_all_md_files(working_dir='.'):
    """Process all Markdown files in the specified directory."""
    # デバッグ情報：指定されたディレクトリ内の全ファイルを表示
    print(f"ディレクトリ '{working_dir}' 内のファイル一覧:")
    try:
        for f in os.listdir(working_dir):
            print(f"  - {f}")
    except Exception as e:
        print(f"Error listing directory {working_dir}: {str(e)}")
        return

    # 絶対パスに変換
    abs_working_dir = os.path.abspath(working_dir)
    print(f"絶対パス: {abs_working_dir}")

    try:
        md_files = [os.path.join(abs_working_dir, f) for f in os.listdir(abs_working_dir) if f.lower().endswith('.md')]
    except Exception as e:
        print(f"Error finding Markdown files: {str(e)}")
        return

    # デバッグ情報：検出されたMarkdownファイルを表示
    print("\n検出されたMarkdownファイル:")
    for f in md_files:
        print(f"  - {f}")

    if not md_files:
        print("\nNo Markdown files found in the current directory.")
        return

    # ファイル名のエンコーディング問題を回避するために、並列処理を使用せずに順次処理する
    results = []
    for md_file in md_files:
        try:
            print(f"\n処理中: {md_file}")
            result = convert_md_to_html(md_file)
            results.append(result)
        except Exception as e:
            print(f"Error processing {md_file}: {str(e)}")
            results.append(False)

    success_count = sum(1 for r in results if r)
    print(f"Processed {success_count} of {len(md_files)} Markdown files.")

def main():
    parser = argparse.ArgumentParser(description='Convert Markdown to HTML with scrollable tables.')
    parser.add_argument('input_file', nargs='?', help='Input Markdown file')
    parser.add_argument('-o', '--output', help='Output HTML file')
    parser.add_argument('--working-dir', default='.', help='Working directory containing Markdown files')

    args = parser.parse_args()

    # デバッグ情報：コマンドライン引数を表示
    print("コマンドライン引数:")
    print(f"  input_file: {args.input_file}")
    print(f"  output: {args.output}")
    print(f"  working_dir: {args.working_dir}")

    if args.input_file:
        # Process a single file
        # 入力ファイルが相対パスの場合、working_dirと結合する
        if not os.path.isabs(args.input_file):
            input_file = os.path.join(args.working_dir, args.input_file)
        else:
            input_file = args.input_file

        # 出力ファイルも同様に処理
        output_file = args.output
        if output_file and not os.path.isabs(output_file):
            output_file = os.path.join(args.working_dir, output_file)

        print(f"処理するファイル: {input_file}")
        if output_file:
            print(f"出力先: {output_file}")

        convert_md_to_html(input_file, output_file)
    else:
        # Process all Markdown files in the specified directory
        process_all_md_files(args.working_dir)

if __name__ == "__main__":
    main()
